export default async function ProductDetails({ params }: { params: { productID: string } }) {
    const productID = params.productID;
    const res = await fetch(`http://localhost:3000/api/products/${productID}`, {
        cache: "no-store",
    });
    const product = await res.json();

    return (
        <div>
            <h1>Product Details</h1>
            <p>ID: {product.id}</p>
            <p>Name: {product.name}</p>
        </div>
    );
}
