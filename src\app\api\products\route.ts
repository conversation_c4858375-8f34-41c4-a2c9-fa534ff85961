import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/products → list all
export async function GET() {
  const products = await prisma.product.findMany();
  return NextResponse.json(products);
}

// POST /api/products → create new
export async function POST(req: Request) {
  const body = await req.json();
  const product = await prisma.product.create({
    data: { name: body.name },
  });
  return NextResponse.json(product, { status: 201 });
}
