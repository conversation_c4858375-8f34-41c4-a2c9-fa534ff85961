export default async function Products() {
   // Fetch your own API route
  const res = await fetch("http://localhost:3000/api/products", {
    cache: "no-store", // always fetch fresh data (disable caching)
  });
  const products = await res.json();

  return <div>
    Products Page
    <ul>
      {products.map((product: { id: number; name: string }) => (
        <li key={product.id}>
          <a href={`/products/${product.id}`}>{product.name}</a>
        </li>
      ))}
    </ul>
    
    </div>;
}
