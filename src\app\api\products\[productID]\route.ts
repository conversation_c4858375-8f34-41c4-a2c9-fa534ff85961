import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/products/:id
export async function GET(
  req: Request,
  { params }: { params: { productID: string } }
) {
  const product = await prisma.product.findUnique({
    where: { id: Number(params.productID) },
  });
  if (!product) return NextResponse.json({ error: "Not found" }, { status: 404 });
  return NextResponse.json(product);
}

// PUT /api/products/:id
export async function PUT(
  req: Request,
  { params }: { params: { productID: string } }
) {
  const body = await req.json();
  const product = await prisma.product.update({
    where: { id: Number(params.productID) },
    data: { name: body.name },
  });
  return NextResponse.json(product);
}

// DELETE /api/products/:id
export async function DELETE(
  req: Request,
  { params }: { params: { productID: string } }
) {
  await prisma.product.delete({
    where: { id: Number(params.productID) },
  });
  return NextResponse.json({ success: true });
}
