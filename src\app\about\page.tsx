"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function About() {
  const [input, setInput] = useState("");
  const router = useRouter();

  // Logic to handle button click
  const handleButtonClick = () => {
    router.push(`/products/${input}`);
  };
  return (
    <div>
      <h1>About Nigga</h1>
      {/* Input */}
      <label>
        Text input: <input className="bg-white text-black" name="myInput" value={input} onChange={(e) => setInput(e.target.value)} />
      </label>

      {/* Button */}
      <button type="button" className="bg-green-500" onClick={handleButtonClick}>Click me</button>
    </div>
  );
}
